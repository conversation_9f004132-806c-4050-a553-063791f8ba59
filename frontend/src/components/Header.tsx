// Server Component for static header parts
import Logo from './Logo';
import HeaderInteractive from './HeaderInteractive';
import { MobileSearchBar } from './MobileSearchBar';

interface HeaderProps {
  onToggleRightSidebar?: () => void;
}

export default function Header({ onToggleRightSidebar }: HeaderProps) {
  return (
    <header className="bg-card border-b container-border py-3">
      <div className="max-w-7xl w-full mx-auto px-4">
        {/* Main header row for all screen sizes */}
        <div className="flex items-center justify-between">
          {/* Left side - Logo and Navigation */}
          <div className="flex items-center space-x-8">
            {/* Logo */}
            <div className="pl-4">
              <Logo />
            </div>

            {/* Navigation Menu */}
            <nav className="hidden md:flex items-center space-x-6">
              <a href="#" className="text-muted-foreground hover:text-primary font-medium">
                News
              </a>
              <a href="#" className="text-muted-foreground hover:text-primary font-medium">
                Transfers
              </a>
              <a href="#" className="text-muted-foreground hover:text-primary font-medium">
                About us
              </a>
            </nav>
          </div>

          {/* Right side - Interactive components */}
          <HeaderInteractive onToggleRightSidebar={onToggleRightSidebar} />
        </div>

        {/* Mobile Search Bar */}
        <MobileSearchBar />
      </div>
    </header>
  );
}